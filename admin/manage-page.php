<?php
/**
 * Git Manager Manage Page
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Check user permissions
if (!current_user_can('manage_options')) {
    wp_die(__('You do not have sufficient permissions to access this page.'));
}

// Get current tab
$current_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'files';

// Define tabs
$tabs = array(
    'files' => 'File Management',
    'commits' => 'Commits',
    'branches' => 'Branches',
    'remotes' => 'Remotes',
    'history' => 'History',
    'advanced' => 'Advanced'
);

// Get Git operations class
include_once plugin_dir_path(__FILE__) . '../includes/class-git-operations.php';
$git_ops = new WPGitManager_GitOperations();

// Get repository information
$git_path = get_option('wpgm_git_path', '/usr/bin/git');
$repo_path = get_option('wpgm_repo_path', ABSPATH);
$status = $git_ops->get_status($git_path, $repo_path);

?>

<div class="wrap">
    <h1>Git Manager - Manage Repository</h1>
    
    <!-- Tab Navigation -->
    <nav class="nav-tab-wrapper">
        <?php foreach ($tabs as $tab_key => $tab_name): ?>
            <a href="<?php echo admin_url('admin.php?page=wp-git-manager-manage&tab=' . $tab_key); ?>" 
               class="nav-tab <?php echo $current_tab === $tab_key ? 'nav-tab-active' : ''; ?>">
                <?php echo esc_html($tab_name); ?>
            </a>
        <?php endforeach; ?>
    </nav>
    
    <div class="tab-content">
        
        <?php if ($current_tab === 'files'): ?>
        <!-- File Management Tab -->
        <div class="tab-pane active">
            <h2>File Management</h2>
            
            <div class="git-actions-bar">
                <button type="button" class="button button-primary git-commit-btn">
                    <span class="dashicons dashicons-yes"></span> Commit Changes
                </button>
                <button type="button" class="button" id="refresh-status">
                    <span class="dashicons dashicons-update"></span> Refresh Status
                </button>
                <button type="button" class="button" id="stage-all">
                    <span class="dashicons dashicons-plus"></span> Stage All
                </button>
                <button type="button" class="button" id="unstage-all">
                    <span class="dashicons dashicons-minus"></span> Unstage All
                </button>
            </div>
            
            <div id="git-file-status">
                <?php if (!empty($status['files'])): ?>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th scope="col" class="manage-column column-cb check-column">
                                <input type="checkbox" id="select-all-files">
                            </th>
                            <th scope="col" class="manage-column">Status</th>
                            <th scope="col" class="manage-column">File</th>
                            <th scope="col" class="manage-column">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($status['files'] as $file): ?>
                        <tr>
                            <th scope="row" class="check-column">
                                <input type="checkbox" name="selected_files[]" value="<?php echo esc_attr($file['name']); ?>">
                            </th>
                            <td>
                                <span class="file-status status-<?php echo esc_attr(strtolower($file['status'])); ?>">
                                    <?php echo esc_html($file['status']); ?>
                                </span>
                            </td>
                            <td>
                                <code><?php echo esc_html($file['name']); ?></code>
                            </td>
                            <td>
                                <button type="button" class="button button-small git-stage-file" data-file="<?php echo esc_attr($file['name']); ?>">
                                    Stage
                                </button>
                                <button type="button" class="button button-small git-unstage-file" data-file="<?php echo esc_attr($file['name']); ?>">
                                    Unstage
                                </button>
                                <button type="button" class="button button-small git-view-diff" data-file="<?php echo esc_attr($file['name']); ?>">
                                    View Diff
                                </button>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                <?php else: ?>
                <div class="notice notice-info">
                    <p>No changes detected in the repository.</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <?php elseif ($current_tab === 'commits'): ?>
        <!-- Commits Tab -->
        <div class="tab-pane active">
            <h2>Commit Operations</h2>
            
            <div class="commit-form">
                <h3>Create New Commit</h3>
                <table class="form-table">
                    <tr>
                        <th scope="row">Commit Message</th>
                        <td>
                            <textarea id="commit-message" rows="3" cols="50" class="large-text" placeholder="Enter commit message..."></textarea>
                            <p class="description">Describe the changes you're committing.</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Options</th>
                        <td>
                            <label>
                                <input type="checkbox" id="auto-stage" checked> 
                                Automatically stage all changes
                            </label>
                        </td>
                    </tr>
                </table>
                <p class="submit">
                    <button type="button" class="button button-primary" id="create-commit">
                        <span class="dashicons dashicons-yes"></span> Create Commit
                    </button>
                </p>
            </div>
            
            <div class="push-pull-actions">
                <h3>Sync with Remote</h3>
                <p>
                    <button type="button" class="button git-push-btn">
                        <span class="dashicons dashicons-upload"></span> Push to Remote
                    </button>
                    <button type="button" class="button git-pull-btn">
                        <span class="dashicons dashicons-download"></span> Pull from Remote
                    </button>
                </p>
            </div>
        </div>
        
        <?php elseif ($current_tab === 'branches'): ?>
        <!-- Branches Tab -->
        <div class="tab-pane active">
            <h2>Branch Management</h2>
            
            <div class="branch-actions">
                <h3>Create New Branch</h3>
                <table class="form-table">
                    <tr>
                        <th scope="row">Branch Name</th>
                        <td>
                            <input type="text" id="new-branch-name" class="regular-text" placeholder="feature/new-feature">
                            <button type="button" class="button" id="create-branch">Create Branch</button>
                            <p class="description">Enter a name for the new branch.</p>
                        </td>
                    </tr>
                </table>
            </div>
            
            <div id="branches-list">
                <h3>Existing Branches</h3>
                <div id="branch-list-content">
                    <button type="button" class="button" id="load-branches">Load Branches</button>
                </div>
            </div>
        </div>
        
        <?php elseif ($current_tab === 'remotes'): ?>
        <!-- Remotes Tab -->
        <div class="tab-pane active">
            <h2>Remote Repository Management</h2>
            
            <div class="remote-actions">
                <h3>Add New Remote</h3>
                <table class="form-table">
                    <tr>
                        <th scope="row">Remote Name</th>
                        <td>
                            <input type="text" id="remote-name" class="regular-text" value="origin" placeholder="origin">
                            <p class="description">Name for the remote repository (e.g., origin).</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Remote URL</th>
                        <td>
                            <input type="url" id="remote-url" class="large-text" placeholder="https://github.com/username/repository.git">
                            <p class="description">URL of the remote repository.</p>
                        </td>
                    </tr>
                </table>
                <p class="submit">
                    <button type="button" class="button button-primary" id="add-remote">
                        <span class="dashicons dashicons-plus"></span> Add Remote
                    </button>
                </p>
            </div>
            
            <div id="remotes-list">
                <h3>Existing Remotes</h3>
                <div id="remote-list-content">
                    <button type="button" class="button" id="load-remotes">Load Remotes</button>
                </div>
            </div>
        </div>
        
        <?php elseif ($current_tab === 'history'): ?>
        <!-- History Tab -->
        <div class="tab-pane active">
            <h2>Commit History</h2>
            
            <div class="history-controls">
                <label for="history-limit">Show last:</label>
                <select id="history-limit">
                    <option value="10">10 commits</option>
                    <option value="25" selected>25 commits</option>
                    <option value="50">50 commits</option>
                    <option value="100">100 commits</option>
                </select>
                <button type="button" class="button" id="load-history">Load History</button>
            </div>
            
            <div id="commit-history">
                <p>Click "Load History" to view commit history.</p>
            </div>
        </div>
        
        <?php elseif ($current_tab === 'advanced'): ?>
        <!-- Advanced Tab -->
        <div class="tab-pane active">
            <h2>Advanced Git Operations</h2>
            
            <div class="advanced-actions">
                <h3>Repository Information</h3>
                <p>
                    <button type="button" class="button" id="view-git-log">View Git Log</button>
                    <button type="button" class="button" id="view-git-status">View Git Status</button>
                    <button type="button" class="button" id="view-git-config">View Git Config</button>
                </p>
                
                <h3>Maintenance</h3>
                <p>
                    <button type="button" class="button" id="git-gc">Garbage Collection</button>
                    <button type="button" class="button" id="git-fsck">File System Check</button>
                </p>
                
                <div class="danger-zone" style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 15px; margin-top: 20px;">
                    <h4 style="margin-top: 0; color: #856404;">⚠️ Danger Zone</h4>
                    <p style="color: #856404;">These operations can be destructive. Use with caution.</p>
                    <p>
                        <button type="button" class="button button-secondary" id="reset-hard">Hard Reset</button>
                        <button type="button" class="button button-secondary" id="clean-untracked">Clean Untracked Files</button>
                    </p>
                </div>
            </div>
        </div>
        
        <?php endif; ?>
        
    </div>
    
    <!-- Output Area -->
    <div id="git-output" style="margin-top: 20px;"></div>
    
</div>

<style>
.tab-content {
    margin-top: 20px;
}

.git-actions-bar {
    margin-bottom: 20px;
    padding: 15px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.git-actions-bar .button {
    margin-right: 10px;
}

.file-status {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 3px;
    font-weight: bold;
    font-size: 12px;
    color: white;
}

.status-m { background-color: #d63638; } /* Modified */
.status-a { background-color: #00a32a; } /* Added */
.status-d { background-color: #d63638; } /* Deleted */
.status-r { background-color: #dba617; } /* Renamed */
.status-\? { background-color: #666; } /* Untracked */

.commit-form, .branch-actions, .remote-actions, .advanced-actions {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.history-controls {
    margin-bottom: 20px;
}

.history-controls select, .history-controls button {
    margin-left: 10px;
}

#commit-history .commit-item {
    border-bottom: 1px solid #eee;
    padding: 10px 0;
}

#commit-history .commit-item:last-child {
    border-bottom: none;
}

.danger-zone .button {
    margin-right: 10px;
}
</style>
