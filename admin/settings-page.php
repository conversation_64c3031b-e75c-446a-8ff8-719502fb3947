<?php

/**
 * Settings page for WP Git Manager
 */
if (!current_user_can('manage_options')) {
    wp_die('Unauthorized');
}

// Handle form submission
if (isset($_POST['submit']) && wp_verify_nonce($_POST['wpgm_settings_nonce'], 'wpgm_settings')) {
    $settings = array(
        'wpgm_git_path' => sanitize_text_field($_POST['git_path']),
        'wpgm_repo_path' => sanitize_text_field($_POST['repo_path']),
        'wpgm_remote_name' => sanitize_text_field($_POST['remote_name']),
        'wpgm_branch_name' => sanitize_text_field($_POST['branch_name']),
        'wpgm_auto_add' => isset($_POST['auto_add']) ? '1' : '0',
        'wpgm_commit_author_name' => sanitize_text_field($_POST['commit_author_name']),
        'wpgm_commit_author_email' => sanitize_email($_POST['commit_author_email']),
        'wpgm_default_commit_message' => sanitize_text_field($_POST['default_commit_message'])
    );

    foreach ($settings as $key => $value) {
        update_option($key, $value);
    }

    // Handle .gitignore update
    if (isset($_POST['gitignore_content'])) {
        $gitignore_content = stripslashes($_POST['gitignore_content']);
        $gitignore_path = get_option('wpgm_repo_path', ABSPATH) . '.gitignore';
        file_put_contents($gitignore_path, $gitignore_content);
    }

    echo '<div class="notice notice-success is-dismissible"><p>Settings saved successfully!</p></div>';
}

// Get current settings
$git_path = get_option('wpgm_git_path', '/usr/bin/git');
$repo_path = get_option('wpgm_repo_path', ABSPATH);
$remote_name = get_option('wpgm_remote_name', 'origin');
$branch_name = get_option('wpgm_branch_name', 'main');
$auto_add = get_option('wpgm_auto_add', '1');
$commit_author_name = get_option('wpgm_commit_author_name', '');
$commit_author_email = get_option('wpgm_commit_author_email', '');
$default_commit_message = get_option('wpgm_default_commit_message', 'WordPress changes');

// Load .gitignore content
$gitignore_path = $repo_path . '.gitignore';
$gitignore_content = file_exists($gitignore_path) ? file_get_contents($gitignore_path) : '';
?>

<div class="wrap">
    <h1>Git Manager Settings</h1>

    <div id="git-status-display">
        <h2>Repository Status</h2>
        <div id="current-status">
            <button type="button" class="button" id="refresh-status">Refresh Status</button>
            <button type="button" class="button" id="view-branches">View Branches</button>
            <button type="button" class="button" id="view-history">View History</button>
            <div id="status-output"></div>
            <div class="git-file-list"></div>
        </div>
    </div>

    <div id="git-enhanced-features" style="background: #fff; border: 1px solid #ccd0d4; border-radius: 4px; padding: 20px; margin-bottom: 20px;">
        <h2>Enhanced Git Features</h2>
        <div id="branch-management">
            <h3>Branch Management</h3>
            <div id="branches-list"></div>
            <div style="margin-top: 10px;">
                <input type="text" id="new-branch-name" placeholder="New branch name" class="regular-text" />
                <button type="button" class="button" id="create-new-branch">Create Branch</button>
            </div>
        </div>
    </div>

    <form method="post" action="">
        <?php wp_nonce_field('wpgm_settings', 'wpgm_settings_nonce'); ?>

        <table class="form-table">
            <tr>
                <th scope="row">Git Path</th>
                <td>
                    <input type="text" name="git_path" value="<?php echo esc_attr($git_path); ?>" class="regular-text" />
                    <p class="description">Path to the Git executable on your server.</p>
                </td>
            </tr>

            <tr>
                <th scope="row">Repository Path</th>
                <td>
                    <input type="text" name="repo_path" value="<?php echo esc_attr($repo_path); ?>" class="regular-text" />
                    <p class="description">Path to your WordPress installation directory.</p>
                </td>
            </tr>

            <tr>
                <th scope="row">Remote Name</th>
                <td>
                    <input type="text" name="remote_name" value="<?php echo esc_attr($remote_name); ?>" class="regular-text" />
                    <p class="description">Name of the remote repository (usually 'origin').</p>
                </td>
            </tr>

            <tr>
                <th scope="row">Branch Name</th>
                <td>
                    <input type="text" name="branch_name" value="<?php echo esc_attr($branch_name); ?>" class="regular-text" />
                    <p class="description">Default branch name (e.g., 'main', 'master', 'develop').</p>
                </td>
            </tr>

            <tr>
                <th scope="row">Auto Add Files</th>
                <td>
                    <label>
                        <input type="checkbox" name="auto_add" value="1" <?php checked($auto_add, '1'); ?> />
                        Automatically add all files when committing
                    </label>
                    <p class="description">When enabled, 'git add -A' will be run before each commit.</p>
                </td>
            </tr>

            <tr>
                <th scope="row">Commit Author Name</th>
                <td>
                    <input type="text" name="commit_author_name" value="<?php echo esc_attr($commit_author_name); ?>" class="regular-text" />
                    <p class="description">Name to use for Git commits (leave empty to use global Git config).</p>
                </td>
            </tr>

            <tr>
                <th scope="row">Commit Author Email</th>
                <td>
                    <input type="email" name="commit_author_email" value="<?php echo esc_attr($commit_author_email); ?>" class="regular-text" />
                    <p class="description">Email to use for Git commits (leave empty to use global Git config).</p>
                </td>
            </tr>

            <tr>
                <th scope="row">Default Commit Message</th>
                <td>
                    <input type="text" name="default_commit_message" value="<?php echo esc_attr($default_commit_message); ?>" class="regular-text" />
                    <p class="description">Default message to pre-fill in commit dialogs.</p>
                </td>
            </tr>
        </table>

        <h2>Git Ignore Configuration</h2>
        <table class="form-table">
            <tr>
                <th scope="row">.gitignore Content</th>
                <td>
                    <textarea name="gitignore_content" rows="15" cols="60" class="large-text code"><?php echo esc_textarea($gitignore_content); ?></textarea>
                    <p class="description">Files and patterns to exclude from Git tracking. One pattern per line.</p>
                    <p class="description">
                        <strong>Common WordPress exclusions:</strong><br>
                        <code>wp-config.php</code> - Database credentials<br>
                        <code>wp-content/uploads/</code> - Media files<br>
                        <code>*.log</code> - Log files<br>
                        <code>.DS_Store</code> - macOS system files<br>
                        <code>node_modules/</code> - Node.js dependencies
                    </p>
                </td>
            </tr>
        </table>

        <h2>Repository Actions</h2>
        <table class="form-table">
            <tr>
                <th scope="row">Quick Actions</th>
                <td>
                    <p>
                        <button type="button" class="button" id="test-connection">Test Git Connection</button>
                        <button type="button" class="button" id="view-log">View Git Log</button>
                        <button type="button" class="button" id="view-remotes">View Remotes</button>
                    </p>
                    <div id="action-output"></div>
                </td>
            </tr>
            <tr>
                <th scope="row">Repository Management</th>
                <td>
                    <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 15px; margin-bottom: 15px;">
                        <h4 style="margin-top: 0; color: #856404;">⚠️ Danger Zone</h4>
                        <p style="color: #856404; margin-bottom: 10px;">
                            <strong>Reset Repository:</strong> This will completely remove the .git directory and all Git history.
                            This action cannot be undone and will require you to set up Git again from scratch.
                        </p>
                        <p>
                            <button type="button" class="button button-secondary" id="reset-repository" style="color: #d63384; border-color: #d63384;">
                                Reset Git Repository
                            </button>
                        </p>
                        <div id="reset-output"></div>
                    </div>
                </td>
            </tr>
        </table>

        <?php submit_button(); ?>
    </form>

    <h2>Repository Information</h2>
    <div id="repo-info">
        <button type="button" class="button" id="load-repo-info">Load Repository Information</button>
        <div id="repo-details"></div>
    </div>
</div>

<style>
    #git-status-display {
        background: #fff;
        border: 1px solid #ccd0d4;
        border-radius: 4px;
        padding: 20px;
        margin-bottom: 20px;
    }

    #status-output,
    #action-output,
    #repo-details {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        padding: 15px;
        margin-top: 10px;
        font-family: monospace;
        white-space: pre-wrap;
        max-height: 300px;
        overflow-y: auto;
    }

    .git-status-item {
        padding: 5px 10px;
        margin: 5px 0;
        border-radius: 3px;
    }

    .git-status-clean {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .git-status-changes {
        background: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }

    .git-status-error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    #reset-repository:hover {
        background-color: #d63384 !important;
        color: white !important;
        border-color: #d63384 !important;
    }

    .git-branch-list {
        list-style: none;
        padding: 0;
    }

    .git-branch-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        margin: 4px 0;
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 4px;
    }

    .git-branch-item.current {
        background: #d4edda;
        border-color: #c3e6cb;
    }

    .git-branch-name {
        font-family: monospace;
        font-weight: bold;
    }

    .git-branch-switch {
        background: #007cba;
        color: white;
        border: none;
        padding: 4px 8px;
        border-radius: 3px;
        cursor: pointer;
        font-size: 12px;
    }

    .git-branch-switch:hover {
        background: #005a87;
    }

    .current-indicator {
        color: #155724;
        font-weight: bold;
        font-size: 12px;
    }
</style>

<script type="text/javascript">
    jQuery(document).ready(function($) {

        // Refresh status functionality
        $('#refresh-status').on('click', function() {
            var button = $(this);
            button.prop('disabled', true).text('Refreshing...');

            $.post(ajaxurl, {
                    action: 'git_status',
                    nonce: wpGitManager.nonce
                })
                .done(function(response) {
                    if (response.success) {
                        var status = response.data;
                        var html = '<div class="git-status-item ';

                        if (status.changes_count === 0) {
                            html += 'git-status-clean">✓ Repository is clean - no changes to commit';
                        } else {
                            html += 'git-status-changes">⚠ ' + status.changes_count + ' file(s) have changes';
                        }

                        html += '</div>';

                        if (status.changes && status.changes.length > 0) {
                            html += '<h4>Changed Files:</h4><ul>';
                            status.changes.forEach(function(change) {
                                html += '<li><code>' + change + '</code></li>';
                            });
                            html += '</ul>';
                        }

                        $('#status-output').html(html);
                    } else {
                        $('#status-output').html('<div class="git-status-item git-status-error">✗ Error: ' + response.data + '</div>');
                    }
                })
                .fail(function() {
                    $('#status-output').html('<div class="git-status-item git-status-error">✗ Failed to get status</div>');
                })
                .always(function() {
                    button.prop('disabled', false).text('Refresh Status');
                });
        });

        // Test connection
        $('#test-connection').on('click', function() {
            var button = $(this);
            button.prop('disabled', true).text('Testing...');

            var gitPath = $('input[name="git_path"]').val();

            $.post(ajaxurl, {
                    action: 'git_setup_check',
                    nonce: wpGitManager.nonce,
                    check_type: 'git_path',
                    git_path: gitPath
                })
                .done(function(response) {
                    if (response.success) {
                        $('#action-output').html('✓ Git connection successful!\nVersion: ' + response.data.version);
                    } else {
                        $('#action-output').html('✗ Git connection failed: ' + response.data);
                    }
                })
                .always(function() {
                    button.prop('disabled', false).text('Test Git Connection');
                });
        });

        // View git log
        $('#view-log').on('click', function() {
            var button = $(this);
            button.prop('disabled', true).text('Loading...');

            // Use the git operations to get log
            $.post(ajaxurl, {
                    action: 'git_status', // We'll extend this to handle log requests
                    nonce: wpGitManager.nonce,
                    git_command: 'log --oneline -10'
                })
                .done(function(response) {
                    if (response.success) {
                        $('#action-output').html('Recent commits:\n' + (response.data.message || 'No commits found'));
                    } else {
                        $('#action-output').html('Error getting log: ' + response.data);
                    }
                })
                .always(function() {
                    button.prop('disabled', false).text('View Git Log');
                });
        });

        // View remotes
        $('#view-remotes').on('click', function() {
            var button = $(this);
            button.prop('disabled', true).text('Loading...');

            $.post(ajaxurl, {
                    action: 'git_status',
                    nonce: wpGitManager.nonce,
                    git_command: 'remote -v'
                })
                .done(function(response) {
                    if (response.success && response.data.message) {
                        $('#action-output').html('Configured remotes:\n' + response.data.message);
                    } else {
                        $('#action-output').html('No remotes configured or error occurred');
                    }
                })
                .always(function() {
                    button.prop('disabled', false).text('View Remotes');
                });
        });

        // Load repository information
        $('#load-repo-info').on('click', function() {
            var button = $(this);
            button.prop('disabled', true).text('Loading...');

            // Get various repository information
            var commands = [
                'branch --show-current',
                'remote get-url origin',
                'log --oneline -5',
                'status --porcelain'
            ];

            var infoHtml = '';
            var completedRequests = 0;

            commands.forEach(function(command, index) {
                $.post(ajaxurl, {
                        action: 'git_status',
                        nonce: wpGitManager.nonce,
                        git_command: command
                    })
                    .done(function(response) {
                        var title = '';
                        switch (index) {
                            case 0:
                                title = 'Current Branch:';
                                break;
                            case 1:
                                title = 'Origin URL:';
                                break;
                            case 2:
                                title = 'Recent Commits:';
                                break;
                            case 3:
                                title = 'Working Directory:';
                                break;
                        }

                        infoHtml += title + '\n';
                        if (response.success && response.data.message) {
                            infoHtml += response.data.message + '\n\n';
                        } else {
                            infoHtml += 'Not available\n\n';
                        }

                        completedRequests++;
                        if (completedRequests === commands.length) {
                            $('#repo-details').html(infoHtml);
                            button.prop('disabled', false).text('Load Repository Information');
                        }
                    })
                    .fail(function() {
                        completedRequests++;
                        if (completedRequests === commands.length) {
                            button.prop('disabled', false).text('Load Repository Information');
                        }
                    });
            });
        });

        // Enhanced features

        // View branches
        $('#view-branches').on('click', function() {
            var button = $(this);
            button.prop('disabled', true).text('Loading...');

            $.post(ajaxurl, {
                    action: 'git_get_branches',
                    nonce: wpGitManager.nonce
                })
                .done(function(response) {
                    if (response.success) {
                        var data = response.data;
                        var html = '<h4>Available Branches:</h4><ul class="git-branch-list">';

                        data.branches.forEach(function(branch) {
                            var isCurrent = branch === data.current_branch;
                            html += '<li class="git-branch-item' + (isCurrent ? ' current' : '') + '">';
                            html += '<span class="git-branch-name">' + branch + '</span>';
                            if (!isCurrent) {
                                html += '<button class="git-branch-switch" data-branch="' + branch + '">Switch</button>';
                            } else {
                                html += '<span class="current-indicator">(current)</span>';
                            }
                            html += '</li>';
                        });
                        html += '</ul>';

                        $('#branches-list').html(html);
                    } else {
                        $('#branches-list').html('<p>Error loading branches: ' + response.data + '</p>');
                    }
                })
                .always(function() {
                    button.prop('disabled', false).text('View Branches');
                });
        });

        // View commit history
        $('#view-history').on('click', function() {
            var button = $(this);
            button.prop('disabled', true).text('Loading...');

            $.post(ajaxurl, {
                    action: 'git_commit_history',
                    nonce: wpGitManager.nonce,
                    limit: 15
                })
                .done(function(response) {
                    if (response.success) {
                        var commits = response.data.commits;
                        var html = '<h4>Recent Commits:</h4><ul>';
                        commits.forEach(function(commit) {
                            html += '<li><code>' + commit + '</code></li>';
                        });
                        html += '</ul>';
                        $('#action-output').html(html);
                    } else {
                        $('#action-output').html('Error loading history: ' + response.data);
                    }
                })
                .always(function() {
                    button.prop('disabled', false).text('View History');
                });
        });

        // Create new branch
        $('#create-new-branch').on('click', function() {
            var branchName = $('#new-branch-name').val().trim();
            if (!branchName) {
                alert('Please enter a branch name');
                return;
            }

            var button = $(this);
            button.prop('disabled', true).text('Creating...');

            $.post(ajaxurl, {
                    action: 'git_create_branch',
                    nonce: wpGitManager.nonce,
                    git_path: $('input[name="git_path"]').val(),
                    repo_path: $('input[name="repo_path"]').val(),
                    branch_name: branchName
                })
                .done(function(response) {
                    if (response.success) {
                        alert('Branch created successfully!');
                        $('#new-branch-name').val('');
                        $('#view-branches').trigger('click');
                    } else {
                        alert('Error creating branch: ' + response.data);
                    }
                })
                .always(function() {
                    button.prop('disabled', false).text('Create Branch');
                });
        });

        // Reset repository functionality
        $('#reset-repository').on('click', function() {
            var confirmMessage = 'WARNING: This will completely remove the Git repository and all history!\n\n' +
                'This action cannot be undone. You will need to set up Git again from scratch.\n\n' +
                'Type "RESET_REPOSITORY" to confirm:';

            var confirmation = prompt(confirmMessage);

            if (confirmation !== 'RESET_REPOSITORY') {
                if (confirmation !== null) {
                    alert('Reset cancelled. You must type "RESET_REPOSITORY" exactly to confirm.');
                }
                return;
            }

            var button = $(this);
            button.prop('disabled', true).text('Resetting...');

            $.post(ajaxurl, {
                    action: 'git_reset_repository',
                    nonce: wpGitManager.nonce,
                    confirm: 'RESET_REPOSITORY',
                    repo_path: $('input[name="repo_path"]').val()
                })
                .done(function(response) {
                    if (response.success) {
                        $('#reset-output').html('<div style="color: #155724; background: #d4edda; border: 1px solid #c3e6cb; padding: 10px; border-radius: 4px; margin-top: 10px;">✓ ' + response.data + '</div>');

                        // Refresh the page after a short delay to show the updated state
                        setTimeout(function() {
                            window.location.reload();
                        }, 2000);
                    } else {
                        $('#reset-output').html('<div style="color: #721c24; background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; border-radius: 4px; margin-top: 10px;">✗ Error: ' + response.data + '</div>');
                    }
                })
                .fail(function() {
                    $('#reset-output').html('<div style="color: #721c24; background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; border-radius: 4px; margin-top: 10px;">✗ Failed to reset repository</div>');
                })
                .always(function() {
                    button.prop('disabled', false).text('Reset Git Repository');
                });
        });

        // Load repository info functionality
        $('#load-repo-info').on('click', function() {
            var button = $(this);
            button.prop('disabled', true).text('Loading...');

            $.post(ajaxurl, {
                    action: 'git_get_repository_info',
                    nonce: wpGitManager.nonce
                })
                .done(function(response) {
                    if (response.success) {
                        var info = response.data;
                        var html = '';

                        if (info.exists) {
                            html += '<h4>Repository Information:</h4>';
                            html += '<p><strong>Path:</strong> ' + info.path + '</p>';
                            html += '<p><strong>Git Binary:</strong> ' + info.git_path + '</p>';
                            html += '<p><strong>Repository Size:</strong> ' + info.size_formatted + '</p>';

                            if (info.branch) {
                                html += '<p><strong>Current Branch:</strong> ' + info.branch + '</p>';
                            }

                            if (info.last_commit) {
                                html += '<p><strong>Last Commit:</strong></p>';
                                html += '<ul>';
                                html += '<li><strong>ID:</strong> ' + info.last_commit.id + '</li>';
                                html += '<li><strong>Subject:</strong> ' + info.last_commit.subject + '</li>';
                                html += '<li><strong>Author:</strong> ' + info.last_commit.author + '</li>';
                                html += '<li><strong>Date:</strong> ' + info.last_commit.date + '</li>';
                                html += '</ul>';
                            }

                            if (info.remotes && info.remotes.length > 0) {
                                html += '<p><strong>Remotes:</strong></p>';
                                html += '<ul>';
                                info.remotes.forEach(function(remote) {
                                    html += '<li><code>' + remote + '</code></li>';
                                });
                                html += '</ul>';
                            }
                        } else {
                            html = '<p>No Git repository found at the specified path.</p>';
                        }

                        $('#repo-details').html(html);
                    } else {
                        $('#repo-details').html('<p>Error loading repository information: ' + response.data + '</p>');
                    }
                })
                .fail(function() {
                    $('#repo-details').html('<p>Failed to load repository information</p>');
                })
                .always(function() {
                    button.prop('disabled', false).text('Load Repository Information');
                });
        });

        // Auto-refresh status on page load
        $('#refresh-status').trigger('click');
    });
</script>