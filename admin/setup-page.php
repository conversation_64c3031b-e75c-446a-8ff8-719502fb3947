<?php

/**
 * Setup page for WP Git Manager
 */

if (!current_user_can('manage_options')) {
    wp_die('Unauthorized');
}
?>

<div class="wrap">
    <div class="setup-breadcrumb" style="margin-bottom: 20px; padding: 10px; background: #f9f9f9; border-left: 4px solid #0073aa; border-radius: 4px;">
        <p style="margin: 0;">
            <a href="<?php echo admin_url('admin.php?page=wp-git-manager'); ?>">Git Manager</a> &raquo;
            <a href="<?php echo admin_url('admin.php?page=wp-git-manager-settings'); ?>">Settings</a> &raquo;
            <strong>Setup Guide</strong>
        </p>
    </div>

    <h1>Git Manager Setup</h1>

    <div id="setup-container">
        <div class="setup-step" id="step-1">
            <h2>Step 1: Git Configuration</h2>
            <p>First, let's verify that Git is available on your server.</p>

            <table class="form-table">
                <tr>
                    <th scope="row">Git Path</th>
                    <td>
                        <input type="text" id="git-path" class="regular-text" value="<?php echo esc_attr(get_option('wpgm_git_path', '/usr/bin/git')); ?>" />
                        <p class="description">Path to the Git executable on your server.</p>
                        <div id="git-path-status"></div>
                    </td>
                </tr>
                <tr>
                    <th scope="row">Repository Path</th>
                    <td>
                        <input type="text" id="repo-path" class="regular-text" value="<?php echo esc_attr(get_option('wpgm_repo_path', ABSPATH)); ?>" />
                        <p class="description">Path to your WordPress installation directory.</p>
                    </td>
                </tr>
            </table>

            <p class="submit">
                <button type="button" class="button button-primary" id="test-git">Test Git Path</button>
                <button type="button" class="button" id="check-setup" style="display:none;">Check Setup Status</button>
            </p>
        </div>

        <div class="setup-step" id="step-2" style="display:none;">
            <h2>Step 2: Repository Configuration</h2>
            <p>Please provide the following information to set up your Git repository:</p>

            <div id="setup-status"></div>

            <div id="config-form">
                <h3>Git User Configuration</h3>
                <table class="form-table">
                    <tr>
                        <th scope="row">User Name</th>
                        <td>
                            <input type="text" id="git-user-name" class="regular-text" placeholder="Your Name" required />
                            <p class="description">Your name for Git commits</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">User Email</th>
                        <td>
                            <input type="email" id="git-user-email" class="regular-text" placeholder="<EMAIL>" required />
                            <p class="description">Your email for Git commits</p>
                        </td>
                    </tr>
                </table>

                <h3>Initial Branch Configuration</h3>
                <table class="form-table">
                    <tr>
                        <th scope="row">Branch Name</th>
                        <td>
                            <input type="text" id="branch-name" class="regular-text" value="main" required />
                            <p class="description">Name for the initial branch (e.g., main, master, develop)</p>
                        </td>
                    </tr>
                </table>

                <h3>Remote Repository (Optional)</h3>
                <table class="form-table">
                    <tr>
                        <th scope="row">Remote Name</th>
                        <td>
                            <input type="text" id="remote-name" class="regular-text" value="origin" />
                            <p class="description">Name for the remote repository</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Remote URL</th>
                        <td>
                            <input type="url" id="remote-url" class="regular-text" placeholder="https://github.com/username/repo.git" />
                            <p class="description">Optional: Add a remote repository URL (GitHub, GitLab, etc.)</p>
                        </td>
                    </tr>
                </table>

                <p class="submit">
                    <button type="button" class="button button-primary" id="start-setup">Initialize Repository</button>
                </p>
            </div>

            <div id="setup-progress" style="display:none;">
                <h3>Setting up repository...</h3>
                <div id="progress-status">
                    <div class="progress-item" id="progress-init">○ Initializing repository...</div>
                    <div class="progress-item" id="progress-user">○ Setting user configuration...</div>
                    <div class="progress-item" id="progress-remote">○ Adding remote repository...</div>
                    <div class="progress-item" id="progress-files">○ Creating initial files...</div>
                </div>
            </div>
        </div>

        <div class="setup-step" id="step-3" style="display:none;">
            <h2>Step 3: Final Configuration</h2>
            <p>Save your settings and complete the setup.</p>

            <div id="final-settings">
                <table class="form-table">
                    <tr>
                        <th scope="row">Auto-add Files</th>
                        <td>
                            <label>
                                <input type="checkbox" id="auto-add" checked />
                                Automatically add all files when committing
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Exclude Files</th>
                        <td>
                            <textarea id="gitignore-content" rows="10" cols="50" placeholder="wp-config.php&#10;*.log&#10;node_modules/&#10;.DS_Store"></textarea>
                            <p class="description">Files and patterns to exclude from Git (will create/update .gitignore)</p>
                        </td>
                    </tr>
                </table>
            </div>

            <p class="submit">
                <button type="button" class="button button-primary" id="complete-setup">Complete Setup</button>
            </p>
        </div>

        <div id="setup-complete" style="display:none;">
            <div class="notice notice-success">
                <p><strong>Setup Complete!</strong> Your Git repository is now ready to use.</p>
                <p>
                    <a href="<?php echo admin_url('admin.php?page=wp-git-manager'); ?>" class="button button-primary">
                        <span class="dashicons dashicons-dashboard"></span> Go to Dashboard
                    </a>
                    <a href="<?php echo admin_url('admin.php?page=wp-git-manager-settings'); ?>" class="button">
                        <span class="dashicons dashicons-admin-settings"></span> Go to Settings
                    </a>
                    <a href="<?php echo admin_url('admin.php?page=wp-git-manager-manage'); ?>" class="button">
                        <span class="dashicons dashicons-admin-tools"></span> Manage Repository
                    </a>
                </p>
            </div>
        </div>
    </div>
</div>

<style>
    .setup-step {
        background: #fff;
        border: 1px solid #ccd0d4;
        border-radius: 4px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .setup-step h2 {
        margin-top: 0;
        border-bottom: 1px solid #eee;
        padding-bottom: 10px;
    }

    #setup-status {
        background: #f1f1f1;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .status-item {
        margin-bottom: 10px;
        padding: 5px 10px;
        border-radius: 3px;
    }

    .status-item.success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .status-item.error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .status-item.warning {
        background: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }

    #git-path-status {
        margin-top: 10px;
    }

    .progress-item {
        margin-bottom: 10px;
        padding: 8px 12px;
        border-radius: 3px;
        background: #f1f1f1;
        border: 1px solid #ddd;
    }

    .progress-item.in-progress {
        background: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }

    .progress-item.success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .progress-item.error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
</style>

<script type="text/javascript">
    jQuery(document).ready(function($) {

        $('#test-git').on('click', function() {
            var gitPath = $('#git-path').val();
            var button = $(this);

            button.prop('disabled', true).text('Testing...');

            $.post(ajaxurl, {
                    action: 'git_setup_check',
                    nonce: wpGitManager.nonce,
                    check_type: 'git_path',
                    git_path: gitPath
                })
                .done(function(response) {
                    if (response.success) {
                        $('#git-path-status').html('<div class="notice notice-success inline"><p>✓ Git found: ' + response.data.version + '</p></div>');
                        $('#check-setup').show();
                    } else {
                        $('#git-path-status').html('<div class="notice notice-error inline"><p>✗ ' + response.data + '</p></div>');
                    }
                })
                .fail(function() {
                    $('#git-path-status').html('<div class="notice notice-error inline"><p>✗ Failed to test Git path</p></div>');
                })
                .always(function() {
                    button.prop('disabled', false).text('Test Git Path');
                });
        });

        $('#check-setup').on('click', function() {
            var gitPath = $('#git-path').val();
            var repoPath = $('#repo-path').val();
            var button = $(this);

            button.prop('disabled', true).text('Checking...');

            $.post(ajaxurl, {
                    action: 'git_setup_check',
                    nonce: wpGitManager.nonce,
                    check_type: 'full_status',
                    git_path: gitPath,
                    repo_path: repoPath
                })
                .done(function(response) {
                    if (response.success) {
                        displaySetupStatus(response.data);
                        $('#step-2').show();
                    }
                })
                .always(function() {
                    button.prop('disabled', false).text('Check Setup Status');
                });
        });

        function displaySetupStatus(status) {
            var html = '<h3>Setup Status</h3>';

            html += '<div class="status-item ' + (status.git_available ? 'success' : 'error') + '">';
            html += (status.git_available ? '✓' : '✗') + ' Git Available</div>';

            html += '<div class="status-item ' + (status.repo_exists ? 'success' : 'warning') + '">';
            html += (status.repo_exists ? '✓' : '○') + ' Git Repository</div>';

            html += '<div class="status-item ' + (status.user_configured ? 'success' : 'warning') + '">';
            html += (status.user_configured ? '✓' : '○') + ' User Configuration</div>';

            html += '<div class="status-item ' + (status.has_remote ? 'success' : 'warning') + '">';
            html += (status.has_remote ? '✓' : '○') + ' Remote Repository</div>';

            html += '<div class="status-item ' + (status.has_branch ? 'success' : 'warning') + '">';
            html += (status.has_branch ? '✓' : '○') + ' Initial Branch</div>';

            $('#setup-status').html(html);

            // If setup is already complete, show step 3
            if (status.is_complete) {
                $('#step-3').show();
            }
        }

        function updateProgress(step, status, message) {
            var element = $('#progress-' + step);
            element.removeClass('in-progress success error');

            if (status === 'in-progress') {
                element.addClass('in-progress').text('⟳ ' + message);
            } else if (status === 'success') {
                element.addClass('success').text('✓ ' + message);
            } else if (status === 'error') {
                element.addClass('error').text('✗ ' + message);
            }
        }

        // Handle new setup flow
        $('#start-setup').on('click', function() {
            var userName = $('#git-user-name').val();
            var userEmail = $('#git-user-email').val();
            var branchName = $('#branch-name').val();
            var remoteName = $('#remote-name').val();
            var remoteUrl = $('#remote-url').val();

            if (!userName || !userEmail || !branchName) {
                alert('Please fill in all required fields (User Name, User Email, and Branch Name)');
                return;
            }

            var button = $(this);
            button.prop('disabled', true).text('Setting up...');

            // Hide form and show progress
            $('#config-form').hide();
            $('#setup-progress').show();

            // Start the setup process
            startSetupProcess({
                gitPath: $('#git-path').val(),
                repoPath: $('#repo-path').val(),
                userName: userName,
                userEmail: userEmail,
                branchName: branchName,
                remoteName: remoteName,
                remoteUrl: remoteUrl
            });
        });

        function startSetupProcess(config) {
            // Step 1: Initialize repository with branch
            updateProgress('init', 'in-progress', 'Initializing repository with branch "' + config.branchName + '"...');

            $.post(ajaxurl, {
                    action: 'git_init_repo_with_branch',
                    nonce: wpGitManager.nonce,
                    git_path: config.gitPath,
                    repo_path: config.repoPath,
                    branch_name: config.branchName
                })
                .done(function(response) {
                    if (response.success) {
                        updateProgress('init', 'success', 'Repository initialized with branch "' + config.branchName + '"');
                        setupUserConfig(config);
                    } else {
                        updateProgress('init', 'error', 'Failed to initialize repository: ' + response.data);
                        resetSetupForm();
                    }
                })
                .fail(function() {
                    updateProgress('init', 'error', 'Failed to initialize repository');
                    resetSetupForm();
                });
        }

        function setupUserConfig(config) {
            updateProgress('user', 'in-progress', 'Setting user configuration...');

            $.post(ajaxurl, {
                    action: 'git_set_user',
                    nonce: wpGitManager.nonce,
                    git_path: config.gitPath,
                    repo_path: config.repoPath,
                    user_name: config.userName,
                    user_email: config.userEmail
                })
                .done(function(response) {
                    if (response.success) {
                        updateProgress('user', 'success', 'User configuration set');
                        if (config.remoteUrl) {
                            setupRemote(config);
                        } else {
                            updateProgress('remote', 'success', 'Remote setup skipped');
                            createInitialFiles(config);
                        }
                    } else {
                        updateProgress('user', 'error', 'Failed to set user configuration: ' + response.data);
                        resetSetupForm();
                    }
                })
                .fail(function() {
                    updateProgress('user', 'error', 'Failed to set user configuration');
                    resetSetupForm();
                });
        }

        function setupRemote(config) {
            updateProgress('remote', 'in-progress', 'Adding remote repository...');

            $.post(ajaxurl, {
                    action: 'git_add_remote',
                    nonce: wpGitManager.nonce,
                    git_path: config.gitPath,
                    repo_path: config.repoPath,
                    remote_name: config.remoteName,
                    remote_url: config.remoteUrl
                })
                .done(function(response) {
                    if (response.success) {
                        updateProgress('remote', 'success', 'Remote repository added');
                        createInitialFiles(config);
                    } else {
                        updateProgress('remote', 'error', 'Failed to add remote: ' + response.data);
                        resetSetupForm();
                    }
                })
                .fail(function() {
                    updateProgress('remote', 'error', 'Failed to add remote repository');
                    resetSetupForm();
                });
        }

        function createInitialFiles(config) {
            updateProgress('files', 'in-progress', 'Creating initial files and commit...');

            $.post(ajaxurl, {
                    action: 'git_create_initial_commit',
                    nonce: wpGitManager.nonce,
                    git_path: config.gitPath,
                    repo_path: config.repoPath,
                    branch_name: config.branchName
                })
                .done(function(response) {
                    if (response.success) {
                        updateProgress('files', 'success', 'Initial files created and committed');
                        completeSetup(config);
                    } else {
                        updateProgress('files', 'error', 'Failed to create initial files: ' + response.data);
                        resetSetupForm();
                    }
                })
                .fail(function() {
                    updateProgress('files', 'error', 'Failed to create initial files');
                    resetSetupForm();
                });
        }

        function completeSetup(config) {
            // Save all settings
            var settings = {
                git_path: config.gitPath,
                repo_path: config.repoPath,
                remote_name: config.remoteName || 'origin',
                branch_name: config.branchName,
                auto_add: '1'
            };

            $.post(ajaxurl, {
                    action: 'git_setup_check',
                    nonce: wpGitManager.nonce,
                    check_type: 'save_settings',
                    settings: settings
                })
                .done(function(response) {
                    if (response.success) {
                        setTimeout(function() {
                            $('#step-2').hide();
                            $('#step-3').show();
                        }, 1000);
                    } else {
                        alert('Error saving settings: ' + response.data);
                        resetSetupForm();
                    }
                })
                .fail(function() {
                    alert('Failed to save settings');
                    resetSetupForm();
                });
        }

        function resetSetupForm() {
            $('#setup-progress').hide();
            $('#config-form').show();
            $('#start-setup').prop('disabled', false).text('Initialize Repository');
        }

        // Handle setup completion
        $('#complete-setup').on('click', function() {
            var settings = {
                git_path: $('#git-path').val(),
                repo_path: $('#repo-path').val(),
                remote_name: $('#remote-name').val() || 'origin',
                branch_name: $('#branch-name').val() || 'main',
                auto_add: $('#auto-add').is(':checked') ? '1' : '0'
            };

            // Create .gitignore if content provided
            var gitignoreContent = $('#gitignore-content').val();
            if (gitignoreContent) {
                settings.gitignore_content = gitignoreContent;
            }

            var button = $(this);
            button.prop('disabled', true).text('Completing...');

            $.post(ajaxurl, {
                    action: 'git_setup_check',
                    nonce: wpGitManager.nonce,
                    check_type: 'save_settings',
                    settings: settings
                })
                .done(function(response) {
                    if (response.success) {
                        $('#step-3').hide();
                        $('#setup-complete').show();
                    } else {
                        alert('Error saving settings: ' + response.data);
                    }
                })
                .always(function() {
                    button.prop('disabled', false).text('Complete Setup');
                });
        });
    });
</script>